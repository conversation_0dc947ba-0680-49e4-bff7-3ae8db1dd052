from rest_framework import serializers
from .models import Product, Category, Supplier


class CategorySerializer(serializers.ModelSerializer):
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'is_active', 'created_at', 'product_count']
        read_only_fields = ['created_at']
    
    def get_product_count(self, obj):
        return obj.products.count()


class SupplierSerializer(serializers.ModelSerializer):
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'email', 'phone', 'address',
            'is_active', 'created_at', 'product_count'
        ]
        read_only_fields = ['created_at']
    
    def get_product_count(self, obj):
        return obj.product_set.count()


class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    profit_margin = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    stock_value = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'sku', 'barcode', 'description', 'category', 'category_name',
            'supplier', 'supplier_name', 'cost_price', 'selling_price', 'stock_quantity',
            'minimum_stock', 'maximum_stock', 'unit', 'weight', 'dimensions',
            'is_active', 'is_featured', 'image', 'created_at', 'updated_at',
            'is_low_stock', 'profit_margin', 'stock_value'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_sku(self, value):
        """Ensure SKU is unique"""
        if self.instance:
            # Updating existing product
            if Product.objects.exclude(pk=self.instance.pk).filter(sku=value).exists():
                raise serializers.ValidationError("A product with this SKU already exists.")
        else:
            # Creating new product
            if Product.objects.filter(sku=value).exists():
                raise serializers.ValidationError("A product with this SKU already exists.")
        return value
    
    def validate(self, data):
        """Validate product data"""
        if data.get('minimum_stock', 0) > data.get('maximum_stock', 0):
            raise serializers.ValidationError("Minimum stock cannot be greater than maximum stock.")
        
        if data.get('cost_price', 0) > data.get('selling_price', 0):
            raise serializers.ValidationError("Cost price cannot be greater than selling price.")
        
        return data


class ProductListSerializer(serializers.ModelSerializer):
    """Simplified serializer for product lists"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'sku', 'category_name', 'selling_price',
            'stock_quantity', 'unit', 'is_active', 'is_low_stock'
        ]
