#!/usr/bin/env python3
"""
Setup script for Inventory & Billing Management System
This script helps set up the project for first-time use
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error during {description}:")
        print(f"  Command: {command}")
        print(f"  Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def create_env_file():
    """Create .env file from .env.example if it doesn't exist"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✓ Created .env file from .env.example")
            print("  Please edit .env file with your database credentials")
        else:
            print("✗ .env.example file not found")
            return False
    else:
        print("✓ .env file already exists")
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['media', 'staticfiles', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    return True

def main():
    """Main setup function"""
    print("=" * 60)
    print("Inventory & Billing Management System Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        print("\n⚠️  Failed to install dependencies. Please run manually:")
        print("   pip install -r requirements.txt")
    
    # Run Django migrations
    if not run_command("python manage.py makemigrations", "Creating database migrations"):
        print("\n⚠️  Failed to create migrations")
    
    if not run_command("python manage.py migrate", "Applying database migrations"):
        print("\n⚠️  Failed to apply migrations. Please check your database configuration in .env")
    
    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", "Collecting static files"):
        print("\n⚠️  Failed to collect static files")
    
    # Create superuser prompt
    print("\n" + "=" * 60)
    print("Setup completed!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Edit .env file with your database credentials")
    print("2. Create a superuser account:")
    print("   python manage.py createsuperuser")
    print("3. Start the Django development server:")
    print("   python manage.py runserver")
    print("4. Start the Flask API server (in another terminal):")
    print("   python flask_app/app.py")
    print("\nAccess the application at: http://localhost:8000")
    print("Access the admin panel at: http://localhost:8000/admin")
    print("Access the Flask API at: http://localhost:5000/api")

if __name__ == "__main__":
    main()
