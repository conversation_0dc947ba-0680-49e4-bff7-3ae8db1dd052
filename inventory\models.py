from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from core.models import BaseModel
from products.models import Product


class StockMovement(BaseModel):
    """Track all stock movements (in/out)"""
    MOVEMENT_TYPES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Stock Adjustment'),
        ('sale', 'Sale'),
        ('return', 'Return'),
        ('damaged', 'Damaged'),
        ('expired', 'Expired'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='stock_movements')
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    reference_number = models.CharField(max_length=100, blank=True, help_text="PO, Invoice, or Reference number")
    notes = models.TextField(blank=True)
    
    # Stock levels after this movement
    stock_before = models.IntegerField()
    stock_after = models.IntegerField()
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['product', 'movement_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - {self.get_movement_type_display()} ({self.quantity})"
    
    def save(self, *args, **kwargs):
        # Calculate stock levels
        if not self.pk:  # New record
            self.stock_before = self.product.stock_quantity
            
            if self.movement_type in ['in', 'return']:
                self.stock_after = self.stock_before + self.quantity
            else:  # out, sale, adjustment, damaged, expired
                self.stock_after = self.stock_before - self.quantity
            
            # Update product stock
            self.product.stock_quantity = self.stock_after
            self.product.save()
        
        super().save(*args, **kwargs)


class PurchaseOrder(BaseModel):
    """Purchase orders for restocking"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled'),
    ]
    
    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey('products.Supplier', on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    order_date = models.DateField(default=timezone.now)
    expected_date = models.DateField(null=True, blank=True)
    received_date = models.DateField(null=True, blank=True)
    
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"PO-{self.po_number} - {self.supplier.name}"
    
    def calculate_totals(self):
        """Calculate order totals"""
        items = self.items.all()
        self.subtotal = sum(item.total_cost for item in items)
        self.tax_amount = self.subtotal * 0.1  # 10% tax
        self.total_amount = self.subtotal + self.tax_amount
        self.save()


class PurchaseOrderItem(BaseModel):
    """Items in a purchase order"""
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity_ordered = models.IntegerField(validators=[MinValueValidator(1)])
    quantity_received = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        unique_together = ['purchase_order', 'product']
    
    def __str__(self):
        return f"{self.product.name} - {self.quantity_ordered} units"
    
    @property
    def total_cost(self):
        return self.quantity_ordered * self.unit_cost
    
    @property
    def is_fully_received(self):
        return self.quantity_received >= self.quantity_ordered


class StockAlert(BaseModel):
    """Stock alerts for low inventory"""
    ALERT_TYPES = [
        ('low_stock', 'Low Stock'),
        ('out_of_stock', 'Out of Stock'),
        ('overstock', 'Overstock'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    message = models.TextField()
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['product', 'is_resolved']),
            models.Index(fields=['alert_type', 'is_resolved']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - {self.get_alert_type_display()}"
    
    def resolve(self):
        """Mark alert as resolved"""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.save()


class InventoryAdjustment(BaseModel):
    """Manual inventory adjustments"""
    ADJUSTMENT_TYPES = [
        ('increase', 'Increase'),
        ('decrease', 'Decrease'),
        ('recount', 'Physical Recount'),
    ]
    
    adjustment_number = models.CharField(max_length=50, unique=True)
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES)
    reason = models.TextField()
    total_items = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"ADJ-{self.adjustment_number} - {self.get_adjustment_type_display()}"


class InventoryAdjustmentItem(BaseModel):
    """Items in an inventory adjustment"""
    adjustment = models.ForeignKey(InventoryAdjustment, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    old_quantity = models.IntegerField()
    new_quantity = models.IntegerField()
    difference = models.IntegerField()
    reason = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['adjustment', 'product']
    
    def __str__(self):
        return f"{self.product.name}: {self.old_quantity} → {self.new_quantity}"
    
    def save(self, *args, **kwargs):
        self.difference = self.new_quantity - self.old_quantity
        super().save(*args, **kwargs)
