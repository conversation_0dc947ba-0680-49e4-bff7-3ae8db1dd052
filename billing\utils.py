from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from io import BytesIO
from django.conf import settings
from core.models import SystemSettings


def generate_invoice_pdf(sale):
    """Generate PDF invoice for a sale"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Get system settings
    try:
        system_settings = SystemSettings.objects.first()
        company_name = system_settings.company_name if system_settings else "Your Company"
        company_address = system_settings.company_address if system_settings else ""
        company_phone = system_settings.company_phone if system_settings else ""
        company_email = system_settings.company_email if system_settings else ""
    except:
        company_name = "Your Company"
        company_address = ""
        company_phone = ""
        company_email = ""
    
    # Styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor=colors.darkblue
    )
    
    normal_style = styles['Normal']
    
    # Build PDF content
    story = []
    
    # Company Header
    story.append(Paragraph(company_name, title_style))
    if company_address:
        story.append(Paragraph(company_address, normal_style))
    if company_phone:
        story.append(Paragraph(f"Phone: {company_phone}", normal_style))
    if company_email:
        story.append(Paragraph(f"Email: {company_email}", normal_style))
    
    story.append(Spacer(1, 20))
    
    # Invoice Header
    story.append(Paragraph("INVOICE", heading_style))
    
    # Invoice details table
    invoice_data = [
        ['Invoice Number:', sale.invoice_number],
        ['Date:', sale.sale_date.strftime('%Y-%m-%d')],
        ['Due Date:', sale.due_date.strftime('%Y-%m-%d') if sale.due_date else 'N/A'],
        ['Payment Status:', sale.get_payment_status_display()],
    ]
    
    invoice_table = Table(invoice_data, colWidths=[2*inch, 3*inch])
    invoice_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    story.append(invoice_table)
    story.append(Spacer(1, 20))
    
    # Customer Information
    story.append(Paragraph("Bill To:", heading_style))
    story.append(Paragraph(sale.customer_name, normal_style))
    if sale.customer_phone:
        story.append(Paragraph(f"Phone: {sale.customer_phone}", normal_style))
    if sale.customer_email:
        story.append(Paragraph(f"Email: {sale.customer_email}", normal_style))
    
    story.append(Spacer(1, 20))
    
    # Items table
    items_data = [['Item', 'Qty', 'Unit Price', 'Total']]
    
    for item in sale.items.all():
        items_data.append([
            item.product.name,
            str(item.quantity),
            f"${item.unit_price:.2f}",
            f"${item.total_price:.2f}"
        ])
    
    items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
    items_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Item names left-aligned
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(items_table)
    story.append(Spacer(1, 20))
    
    # Totals
    totals_data = []
    
    if sale.subtotal != sale.total_amount:
        totals_data.append(['Subtotal:', f"${sale.subtotal:.2f}"])
    
    if sale.discount_amount > 0:
        totals_data.append(['Discount:', f"-${sale.discount_amount:.2f}"])
    
    if sale.tax_amount > 0:
        totals_data.append(['Tax:', f"${sale.tax_amount:.2f}"])
    
    totals_data.append(['Total:', f"${sale.total_amount:.2f}"])
    
    if sale.amount_paid > 0:
        totals_data.append(['Amount Paid:', f"${sale.amount_paid:.2f}"])
        if sale.balance_due > 0:
            totals_data.append(['Balance Due:', f"${sale.balance_due:.2f}"])
    
    totals_table = Table(totals_data, colWidths=[4*inch, 2*inch])
    totals_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('LINEABOVE', (0, -1), (-1, -1), 1, colors.black),
    ]))
    
    story.append(totals_table)
    
    # Notes
    if sale.notes:
        story.append(Spacer(1, 20))
        story.append(Paragraph("Notes:", heading_style))
        story.append(Paragraph(sale.notes, normal_style))
    
    # Terms and Conditions
    if sale.terms_conditions:
        story.append(Spacer(1, 20))
        story.append(Paragraph("Terms & Conditions:", heading_style))
        story.append(Paragraph(sale.terms_conditions, normal_style))
    
    # Build PDF
    doc.build(story)
    pdf = buffer.getvalue()
    buffer.close()
    
    return pdf


def generate_invoice_number():
    """Generate next invoice number"""
    from .models import Sale
    
    last_sale = Sale.objects.order_by('-id').first()
    if last_sale and last_sale.invoice_number:
        try:
            last_number = int(last_sale.invoice_number.split('-')[-1])
            return f"INV-{last_number + 1:06d}"
        except (ValueError, IndexError):
            pass
    
    return "INV-000001"


def calculate_tax_amount(subtotal, tax_percentage):
    """Calculate tax amount"""
    if tax_percentage > 0:
        return (subtotal * tax_percentage) / 100
    return 0


def calculate_discount_amount(subtotal, discount_percentage):
    """Calculate discount amount"""
    if discount_percentage > 0:
        return (subtotal * discount_percentage) / 100
    return 0
