from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta

from products.models import Product
from billing.models import Sale, SaleItem
from inventory.models import StockMovement


@login_required
def dashboard(request):
    """Main dashboard view"""
    # Get dashboard statistics
    total_products = Product.objects.count()
    low_stock_products = Product.objects.filter(stock_quantity__lte=10).count()
    
    # Sales statistics
    today = timezone.now().date()
    today_sales = Sale.objects.filter(created_at__date=today).aggregate(
        total=Sum('total_amount'),
        count=Count('id')
    )
    
    # Recent sales
    recent_sales = Sale.objects.select_related('customer').order_by('-created_at')[:5]
    
    # Low stock products
    low_stock_items = Product.objects.filter(stock_quantity__lte=10).order_by('stock_quantity')[:5]
    
    context = {
        'total_products': total_products,
        'low_stock_products': low_stock_products,
        'today_sales_total': today_sales['total'] or 0,
        'today_sales_count': today_sales['count'] or 0,
        'recent_sales': recent_sales,
        'low_stock_items': low_stock_items,
    }
    
    return render(request, 'core/dashboard.html', context)


def login_view(request):
    """User login view"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            next_url = request.GET.get('next', 'dashboard')
            return redirect(next_url)
        else:
            messages.error(request, 'Invalid username or password.')
    
    return render(request, 'core/login.html')


@login_required
def logout_view(request):
    """User logout view"""
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('login')


@login_required
def profile_view(request):
    """User profile view"""
    return render(request, 'core/profile.html')


def home_view(request):
    """Home page view"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    return redirect('login')
