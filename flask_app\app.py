from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity
import os
import sys
from datetime import timedelta

# Add Django project to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_billing.settings')
import django
django.setup()

from django.contrib.auth import authenticate
from products.models import Product, Category
from billing.models import Sale, SaleItem
from inventory.models import StockMovement

app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'your-secret-key')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Initialize extensions
jwt = JWTManager(app)
CORS(app)


@app.route('/api/auth/login', methods=['POST'])
def login():
    """User authentication endpoint"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password required'}), 400
    
    user = authenticate(username=username, password=password)
    if user and user.is_active:
        access_token = create_access_token(identity=user.id)
        return jsonify({
            'access_token': access_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': getattr(user, 'role', 'staff')
            }
        })
    
    return jsonify({'error': 'Invalid credentials'}), 401


@app.route('/api/products', methods=['GET'])
@jwt_required()
def get_products():
    """Get all products"""
    try:
        products = Product.objects.filter(is_active=True).select_related('category')
        
        # Search functionality
        search = request.args.get('search', '')
        if search:
            products = products.filter(name__icontains=search)
        
        # Category filter
        category_id = request.args.get('category')
        if category_id:
            products = products.filter(category_id=category_id)
        
        # Pagination
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        start = (page - 1) * per_page
        end = start + per_page
        
        products_list = []
        for product in products[start:end]:
            products_list.append({
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'category': product.category.name,
                'selling_price': float(product.selling_price),
                'cost_price': float(product.cost_price),
                'stock_quantity': product.stock_quantity,
                'minimum_stock': product.minimum_stock,
                'unit': product.unit,
                'is_low_stock': product.is_low_stock,
            })
        
        return jsonify({
            'products': products_list,
            'total': products.count(),
            'page': page,
            'per_page': per_page
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/products', methods=['POST'])
@jwt_required()
def create_product():
    """Create new product"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'sku', 'category_id', 'selling_price', 'cost_price']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if SKU already exists
        if Product.objects.filter(sku=data['sku']).exists():
            return jsonify({'error': 'SKU already exists'}), 400
        
        # Create product
        product = Product.objects.create(
            name=data['name'],
            sku=data['sku'],
            description=data.get('description', ''),
            category_id=data['category_id'],
            selling_price=data['selling_price'],
            cost_price=data['cost_price'],
            stock_quantity=data.get('stock_quantity', 0),
            minimum_stock=data.get('minimum_stock', 10),
            unit=data.get('unit', 'pcs'),
        )
        
        return jsonify({
            'id': product.id,
            'name': product.name,
            'sku': product.sku,
            'message': 'Product created successfully'
        }), 201
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/products/<int:product_id>', methods=['GET'])
@jwt_required()
def get_product(product_id):
    """Get single product"""
    try:
        product = Product.objects.select_related('category').get(id=product_id)
        
        return jsonify({
            'id': product.id,
            'name': product.name,
            'sku': product.sku,
            'description': product.description,
            'category': {
                'id': product.category.id,
                'name': product.category.name
            },
            'selling_price': float(product.selling_price),
            'cost_price': float(product.cost_price),
            'stock_quantity': product.stock_quantity,
            'minimum_stock': product.minimum_stock,
            'unit': product.unit,
            'is_low_stock': product.is_low_stock,
            'profit_margin': product.profit_margin,
        })
    
    except Product.DoesNotExist:
        return jsonify({'error': 'Product not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/categories', methods=['GET'])
@jwt_required()
def get_categories():
    """Get all categories"""
    try:
        categories = Category.objects.filter(is_active=True)
        categories_list = []
        
        for category in categories:
            categories_list.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'product_count': category.products.count()
            })
        
        return jsonify({'categories': categories_list})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/sales', methods=['GET'])
@jwt_required()
def get_sales():
    """Get all sales"""
    try:
        sales = Sale.objects.all().order_by('-created_at')
        
        # Date filter
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if date_from:
            sales = sales.filter(created_at__date__gte=date_from)
        if date_to:
            sales = sales.filter(created_at__date__lte=date_to)
        
        # Pagination
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        start = (page - 1) * per_page
        end = start + per_page
        
        sales_list = []
        for sale in sales[start:end]:
            sales_list.append({
                'id': sale.id,
                'invoice_number': sale.invoice_number,
                'customer_name': sale.customer_name,
                'total_amount': float(sale.total_amount),
                'created_at': sale.created_at.isoformat(),
                'items_count': sale.items.count()
            })
        
        return jsonify({
            'sales': sales_list,
            'total': sales.count(),
            'page': page,
            'per_page': per_page
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, port=5000)
