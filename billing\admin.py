from django.contrib import admin
from .models import Customer, Sale, SaleItem, Payment, Quote, QuoteItem


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'customer_type', 'is_active', 'created_at')
    list_filter = ('customer_type', 'is_active', 'created_at')
    search_fields = ('name', 'email', 'phone', 'company_name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'email', 'phone', 'customer_type', 'is_active')
        }),
        ('Address', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country')
        }),
        ('Business Details', {
            'fields': ('company_name', 'tax_id'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )


class SaleItemInline(admin.TabularInline):
    model = SaleItem
    extra = 1
    fields = ('product', 'quantity', 'unit_price', 'discount_percentage', 'total_price')
    readonly_fields = ('total_price',)


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    fields = ('amount', 'payment_method', 'payment_date', 'reference_number')
    readonly_fields = ('payment_date',)


@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'customer_name', 'total_amount', 'payment_status', 'sale_date')
    list_filter = ('payment_status', 'payment_method', 'sale_date')
    search_fields = ('invoice_number', 'customer_name', 'customer__name')
    readonly_fields = ('invoice_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at')
    inlines = [SaleItemInline, PaymentInline]
    
    fieldsets = (
        ('Invoice Information', {
            'fields': ('invoice_number', 'customer', 'customer_name', 'customer_phone', 'customer_email')
        }),
        ('Dates', {
            'fields': ('sale_date', 'due_date')
        }),
        ('Amounts', {
            'fields': ('subtotal', 'discount_percentage', 'discount_amount', 'tax_percentage', 'tax_amount', 'total_amount')
        }),
        ('Payment', {
            'fields': ('payment_status', 'payment_method', 'amount_paid', 'balance_due')
        }),
        ('Additional Information', {
            'fields': ('notes', 'terms_conditions'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        else:  # Updating existing object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('sale', 'amount', 'payment_method', 'payment_date')
    list_filter = ('payment_method', 'payment_date')
    search_fields = ('sale__invoice_number', 'reference_number')
    readonly_fields = ('created_at', 'updated_at')


class QuoteItemInline(admin.TabularInline):
    model = QuoteItem
    extra = 1
    fields = ('product', 'quantity', 'unit_price', 'discount_percentage', 'total_price')
    readonly_fields = ('total_price',)


@admin.register(Quote)
class QuoteAdmin(admin.ModelAdmin):
    list_display = ('quote_number', 'customer', 'total_amount', 'status', 'quote_date', 'valid_until')
    list_filter = ('status', 'quote_date', 'valid_until')
    search_fields = ('quote_number', 'customer__name')
    readonly_fields = ('quote_number', 'subtotal', 'total_amount', 'is_expired', 'created_at', 'updated_at')
    inlines = [QuoteItemInline]
    
    fieldsets = (
        ('Quote Information', {
            'fields': ('quote_number', 'customer', 'status')
        }),
        ('Dates', {
            'fields': ('quote_date', 'valid_until', 'is_expired')
        }),
        ('Amounts', {
            'fields': ('subtotal', 'discount_amount', 'tax_amount', 'total_amount')
        }),
        ('Additional Information', {
            'fields': ('notes', 'terms_conditions'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['convert_to_sales']
    
    def convert_to_sales(self, request, queryset):
        converted_count = 0
        for quote in queryset.filter(status='accepted'):
            try:
                quote.convert_to_sale()
                converted_count += 1
            except ValueError:
                pass
        
        self.message_user(request, f'{converted_count} quotes converted to sales.')
    convert_to_sales.short_description = 'Convert accepted quotes to sales'
