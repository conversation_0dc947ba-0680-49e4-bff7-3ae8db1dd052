from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from .models import Sale, Customer, Payment
from .serializers import SaleSerializer, CustomerSerializer, PaymentSerializer


class SaleViewSet(viewsets.ModelViewSet):
    """API ViewSet for Sale model"""
    queryset = Sale.objects.all()
    serializer_class = SaleSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment_status', 'payment_method', 'customer']
    search_fields = ['invoice_number', 'customer_name']
    ordering_fields = ['created_at', 'total_amount', 'sale_date']
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def pending_payments(self, request):
        """Get sales with pending payments"""
        pending_sales = self.queryset.filter(payment_status__in=['pending', 'partial'])
        serializer = self.get_serializer(pending_sales, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue sales"""
        overdue_sales = self.queryset.filter(payment_status='overdue')
        serializer = self.get_serializer(overdue_sales, many=True)
        return Response(serializer.data)


class CustomerViewSet(viewsets.ModelViewSet):
    """API ViewSet for Customer model"""
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'email', 'phone', 'company_name']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class PaymentViewSet(viewsets.ModelViewSet):
    """API ViewSet for Payment model"""
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'sale']
    ordering_fields = ['payment_date', 'amount']
    ordering = ['-payment_date']
