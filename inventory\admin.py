from django.contrib import admin
from .models import StockMovement, PurchaseOrder, PurchaseOrderItem, StockAlert, InventoryAdjustment


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ('product', 'movement_type', 'quantity', 'stock_before', 'stock_after', 'created_at')
    list_filter = ('movement_type', 'created_at', 'product__category')
    search_fields = ('product__name', 'product__sku', 'reference_number')
    readonly_fields = ('stock_before', 'stock_after', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Movement Details', {
            'fields': ('product', 'movement_type', 'quantity', 'unit_cost', 'reference_number')
        }),
        ('Stock Levels', {
            'fields': ('stock_before', 'stock_after'),
            'classes': ('collapse',)
        }),
        ('Additional Info', {
            'fields': ('notes', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 1
    fields = ('product', 'quantity_ordered', 'quantity_received', 'unit_cost', 'total_cost')
    readonly_fields = ('total_cost',)


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ('po_number', 'supplier', 'status', 'order_date', 'total_amount')
    list_filter = ('status', 'order_date', 'supplier')
    search_fields = ('po_number', 'supplier__name')
    inlines = [PurchaseOrderItemInline]
    readonly_fields = ('subtotal', 'tax_amount', 'total_amount', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Order Information', {
            'fields': ('po_number', 'supplier', 'status', 'order_date', 'expected_date', 'received_date')
        }),
        ('Totals', {
            'fields': ('subtotal', 'tax_amount', 'total_amount'),
            'classes': ('collapse',)
        }),
        ('Additional Info', {
            'fields': ('notes', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(StockAlert)
class StockAlertAdmin(admin.ModelAdmin):
    list_display = ('product', 'alert_type', 'is_resolved', 'created_at', 'resolved_at')
    list_filter = ('alert_type', 'is_resolved', 'created_at')
    search_fields = ('product__name', 'product__sku')
    readonly_fields = ('created_at', 'resolved_at')
    
    actions = ['mark_resolved']
    
    def mark_resolved(self, request, queryset):
        for alert in queryset:
            alert.resolve()
        self.message_user(request, f'{queryset.count()} alerts marked as resolved.')
    mark_resolved.short_description = 'Mark selected alerts as resolved'


@admin.register(InventoryAdjustment)
class InventoryAdjustmentAdmin(admin.ModelAdmin):
    list_display = ('adjustment_number', 'adjustment_type', 'total_items', 'created_at')
    list_filter = ('adjustment_type', 'created_at')
    search_fields = ('adjustment_number', 'reason')
    readonly_fields = ('total_items', 'created_at', 'updated_at')
