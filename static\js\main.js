// Main JavaScript file for Inventory & Billing Management System

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete actions
    $('.delete-confirm').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
        }
    });

    // Format currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });

    // Auto-calculate totals in forms
    $('.calculate-total').on('input', function() {
        calculateRowTotal($(this).closest('tr'));
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#searchTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Product selection for POS
    $('.product-card').on('click', function() {
        var productId = $(this).data('product-id');
        var productName = $(this).data('product-name');
        var productPrice = $(this).data('product-price');
        var productStock = $(this).data('product-stock');
        
        addToCart(productId, productName, productPrice, productStock);
    });

    // Quantity change in cart
    $(document).on('change', '.cart-quantity', function() {
        var row = $(this).closest('tr');
        var quantity = parseInt($(this).val());
        var price = parseFloat(row.find('.item-price').text());
        var total = quantity * price;
        
        row.find('.item-total').text(total.toFixed(2));
        updateCartTotal();
    });

    // Remove item from cart
    $(document).on('click', '.remove-item', function() {
        $(this).closest('tr').remove();
        updateCartTotal();
    });

    // Print invoice
    $('.print-invoice').on('click', function() {
        window.print();
    });

    // Export data
    $('.export-data').on('click', function() {
        var format = $(this).data('format');
        var url = $(this).data('url');
        
        if (format && url) {
            window.location.href = url + '?format=' + format;
        }
    });
});

// Calculate row total
function calculateRowTotal(row) {
    var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    var price = parseFloat(row.find('.price-input').val()) || 0;
    var discount = parseFloat(row.find('.discount-input').val()) || 0;
    
    var subtotal = quantity * price;
    var discountAmount = (subtotal * discount) / 100;
    var total = subtotal - discountAmount;
    
    row.find('.total-display').text(total.toFixed(2));
    
    // Update grand total if function exists
    if (typeof updateGrandTotal === 'function') {
        updateGrandTotal();
    }
}

// Add product to cart (POS)
function addToCart(productId, productName, productPrice, productStock) {
    var cartTable = $('#cart-table tbody');
    var existingRow = cartTable.find('tr[data-product-id="' + productId + '"]');
    
    if (existingRow.length > 0) {
        // Update existing item
        var quantityInput = existingRow.find('.cart-quantity');
        var currentQuantity = parseInt(quantityInput.val());
        var newQuantity = currentQuantity + 1;
        
        if (newQuantity <= productStock) {
            quantityInput.val(newQuantity);
            quantityInput.trigger('change');
        } else {
            alert('Insufficient stock available!');
        }
    } else {
        // Add new item
        if (productStock > 0) {
            var newRow = `
                <tr data-product-id="${productId}">
                    <td>${productName}</td>
                    <td>
                        <input type="number" class="form-control cart-quantity" value="1" min="1" max="${productStock}">
                    </td>
                    <td class="item-price">${productPrice}</td>
                    <td class="item-total">${productPrice}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            cartTable.append(newRow);
            updateCartTotal();
        } else {
            alert('Product is out of stock!');
        }
    }
}

// Update cart total
function updateCartTotal() {
    var total = 0;
    $('#cart-table tbody tr').each(function() {
        var itemTotal = parseFloat($(this).find('.item-total').text()) || 0;
        total += itemTotal;
    });
    
    $('#cart-total').text(total.toFixed(2));
    
    // Apply discount and tax if applicable
    var discount = parseFloat($('#discount-percentage').val()) || 0;
    var tax = parseFloat($('#tax-percentage').val()) || 0;
    
    var discountAmount = (total * discount) / 100;
    var taxableAmount = total - discountAmount;
    var taxAmount = (taxableAmount * tax) / 100;
    var grandTotal = taxableAmount + taxAmount;
    
    $('#discount-amount').text(discountAmount.toFixed(2));
    $('#tax-amount').text(taxAmount.toFixed(2));
    $('#grand-total').text(grandTotal.toFixed(2));
}

// Process POS sale
function processSale() {
    var cartItems = [];
    var customerName = $('#customer-name').val() || 'Walk-in Customer';
    var customerPhone = $('#customer-phone').val() || '';
    var paymentMethod = $('#payment-method').val() || 'cash';
    
    // Collect cart items
    $('#cart-table tbody tr').each(function() {
        var productId = $(this).data('product-id');
        var quantity = parseInt($(this).find('.cart-quantity').val());
        var unitPrice = parseFloat($(this).find('.item-price').text());
        
        cartItems.push({
            product_id: productId,
            quantity: quantity,
            unit_price: unitPrice
        });
    });
    
    if (cartItems.length === 0) {
        alert('Please add items to cart before processing sale.');
        return;
    }
    
    // Show loading
    showLoading();
    
    // Send AJAX request
    $.ajax({
        url: '/billing/pos/process/',
        method: 'POST',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        contentType: 'application/json',
        data: JSON.stringify({
            customer_name: customerName,
            customer_phone: customerPhone,
            payment_method: paymentMethod,
            items: cartItems
        }),
        success: function(response) {
            hideLoading();
            if (response.success) {
                alert('Sale processed successfully! Invoice: ' + response.invoice_number);
                // Clear cart and reset form
                clearCart();
                resetPOSForm();
            } else {
                alert('Error: ' + response.error);
            }
        },
        error: function() {
            hideLoading();
            alert('An error occurred while processing the sale.');
        }
    });
}

// Clear cart
function clearCart() {
    $('#cart-table tbody').empty();
    updateCartTotal();
}

// Reset POS form
function resetPOSForm() {
    $('#customer-name').val('Walk-in Customer');
    $('#customer-phone').val('');
    $('#payment-method').val('cash');
    $('#discount-percentage').val('0');
    $('#tax-percentage').val('0');
}

// Show loading overlay
function showLoading() {
    $('body').append(`
        <div class="spinner-overlay">
            <div class="spinner-border-custom"></div>
        </div>
    `);
}

// Hide loading overlay
function hideLoading() {
    $('.spinner-overlay').remove();
}

// Format number as currency
function formatCurrency(amount) {
    return '$' + parseFloat(amount).toFixed(2);
}

// Validate form before submission
function validateForm(formId) {
    var form = $('#' + formId);
    var isValid = true;
    
    form.find('input[required], select[required], textarea[required]').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    return isValid;
}

// Auto-save form data to localStorage
function autoSaveForm(formId) {
    var form = $('#' + formId);
    var formData = {};
    
    form.find('input, select, textarea').each(function() {
        formData[$(this).attr('name')] = $(this).val();
    });
    
    localStorage.setItem(formId + '_autosave', JSON.stringify(formData));
}

// Restore form data from localStorage
function restoreForm(formId) {
    var savedData = localStorage.getItem(formId + '_autosave');
    
    if (savedData) {
        var formData = JSON.parse(savedData);
        var form = $('#' + formId);
        
        $.each(formData, function(name, value) {
            form.find('[name="' + name + '"]').val(value);
        });
    }
}

// Clear saved form data
function clearSavedForm(formId) {
    localStorage.removeItem(formId + '_autosave');
}
