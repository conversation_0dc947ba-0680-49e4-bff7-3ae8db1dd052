from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'stock-movements', api_views.StockMovementViewSet)
router.register(r'purchase-orders', api_views.PurchaseOrderViewSet)
router.register(r'stock-alerts', api_views.StockAlertViewSet)

urlpatterns = [
    path('inventory/', include(router.urls)),
]
