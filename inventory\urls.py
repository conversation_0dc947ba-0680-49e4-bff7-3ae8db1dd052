from django.urls import path
from . import views

urlpatterns = [
    path('', views.inventory_dashboard, name='inventory_dashboard'),
    path('movements/', views.stock_movements, name='stock_movements'),
    path('movements/add/', views.add_stock_movement, name='add_stock_movement'),
    path('purchase-orders/', views.purchase_orders, name='purchase_orders'),
    path('purchase-orders/create/', views.create_purchase_order, name='create_purchase_order'),
    path('purchase-orders/<int:pk>/', views.purchase_order_detail, name='purchase_order_detail'),
    path('alerts/', views.stock_alerts, name='stock_alerts'),
    path('alerts/<int:pk>/resolve/', views.resolve_alert, name='resolve_alert'),
    path('reports/low-stock/', views.low_stock_report, name='low_stock_report'),
    path('reports/valuation/', views.inventory_valuation, name='inventory_valuation'),
]
