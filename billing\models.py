from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
from core.models import BaseModel
from products.models import Product


class Customer(BaseModel):
    """Customer model"""
    name = models.CharField(max_length=200)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, default='USA')
    
    # Customer type
    CUSTOMER_TYPES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
    ]
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='individual')
    
    # Business details
    company_name = models.Char<PERSON>ield(max_length=200, blank=True)
    tax_id = models.CharField(max_length=50, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['email']),
        ]
    
    def __str__(self):
        return self.name
    
    @property
    def full_address(self):
        """Get formatted full address"""
        address_parts = [self.address, self.city, self.state, self.postal_code, self.country]
        return ', '.join([part for part in address_parts if part])


class Sale(BaseModel):
    """Sales/Invoice model"""
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('partial', 'Partially Paid'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('card', 'Credit/Debit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check'),
        ('digital_wallet', 'Digital Wallet'),
    ]
    
    # Invoice details
    invoice_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='sales', null=True, blank=True)
    customer_name = models.CharField(max_length=200, help_text="For walk-in customers")
    customer_phone = models.CharField(max_length=20, blank=True)
    customer_email = models.EmailField(blank=True)
    
    # Sale details
    sale_date = models.DateTimeField(default=timezone.now)
    due_date = models.DateField(null=True, blank=True)
    
    # Amounts
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Payment
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Additional info
    notes = models.TextField(blank=True)
    terms_conditions = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['invoice_number']),
            models.Index(fields=['customer', 'sale_date']),
            models.Index(fields=['payment_status']),
        ]
    
    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.customer_name}"
    
    @property
    def balance_due(self):
        """Calculate remaining balance"""
        return self.total_amount - self.amount_paid
    
    @property
    def is_fully_paid(self):
        """Check if invoice is fully paid"""
        return self.amount_paid >= self.total_amount
    
    def calculate_totals(self):
        """Calculate invoice totals"""
        items = self.items.all()
        self.subtotal = sum(item.total_price for item in items)
        
        # Apply discount
        if self.discount_percentage > 0:
            self.discount_amount = (self.subtotal * self.discount_percentage) / 100
        
        # Calculate tax on discounted amount
        taxable_amount = self.subtotal - self.discount_amount
        if self.tax_percentage > 0:
            self.tax_amount = (taxable_amount * self.tax_percentage) / 100
        
        self.total_amount = taxable_amount + self.tax_amount
        self.save()
    
    def update_payment_status(self):
        """Update payment status based on amount paid"""
        if self.amount_paid == 0:
            self.payment_status = 'pending'
        elif self.amount_paid >= self.total_amount:
            self.payment_status = 'paid'
        else:
            self.payment_status = 'partial'
        
        # Check for overdue
        if self.due_date and self.due_date < timezone.now().date() and self.payment_status != 'paid':
            self.payment_status = 'overdue'
        
        self.save()


class SaleItem(BaseModel):
    """Items in a sale/invoice"""
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        unique_together = ['sale', 'product']
    
    def __str__(self):
        return f"{self.product.name} x {self.quantity}"
    
    @property
    def total_price(self):
        """Calculate total price for this item"""
        subtotal = self.quantity * self.unit_price
        discount = self.discount_amount
        if self.discount_percentage > 0:
            discount = (subtotal * self.discount_percentage) / 100
        return subtotal - discount
    
    def save(self, *args, **kwargs):
        # Update product stock when saving
        if not self.pk:  # New item
            if self.product.can_sell(self.quantity):
                self.product.stock_quantity -= self.quantity
                self.product.save()
                
                # Create stock movement
                from inventory.models import StockMovement
                StockMovement.objects.create(
                    product=self.product,
                    movement_type='sale',
                    quantity=self.quantity,
                    reference_number=self.sale.invoice_number,
                    created_by=self.sale.created_by
                )
            else:
                raise ValueError(f"Insufficient stock for {self.product.name}")
        
        super().save(*args, **kwargs)


class Payment(BaseModel):
    """Payment records for sales"""
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0.01)])
    payment_method = models.CharField(max_length=20, choices=Sale.PAYMENT_METHOD_CHOICES)
    payment_date = models.DateTimeField(default=timezone.now)
    reference_number = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"Payment {self.amount} for {self.sale.invoice_number}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update sale's amount paid and payment status
        self.sale.amount_paid = self.sale.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or 0
        self.sale.update_payment_status()


class Quote(BaseModel):
    """Sales quotes/estimates"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]
    
    quote_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='quotes')
    
    # Quote details
    quote_date = models.DateField(default=timezone.now)
    valid_until = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Amounts (similar to Sale)
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    notes = models.TextField(blank=True)
    terms_conditions = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Quote {self.quote_number} - {self.customer.name}"
    
    @property
    def is_expired(self):
        """Check if quote is expired"""
        return self.valid_until < timezone.now().date()
    
    def convert_to_sale(self):
        """Convert quote to sale"""
        if self.status != 'accepted':
            raise ValueError("Only accepted quotes can be converted to sales")
        
        # Create sale from quote
        sale = Sale.objects.create(
            customer=self.customer,
            customer_name=self.customer.name,
            customer_phone=self.customer.phone,
            customer_email=self.customer.email,
            subtotal=self.subtotal,
            discount_amount=self.discount_amount,
            tax_amount=self.tax_amount,
            total_amount=self.total_amount,
            notes=self.notes,
            terms_conditions=self.terms_conditions,
            created_by=self.created_by
        )
        
        # Copy quote items to sale items
        for quote_item in self.items.all():
            SaleItem.objects.create(
                sale=sale,
                product=quote_item.product,
                quantity=quote_item.quantity,
                unit_price=quote_item.unit_price,
                discount_percentage=quote_item.discount_percentage,
                discount_amount=quote_item.discount_amount
            )
        
        return sale


class QuoteItem(BaseModel):
    """Items in a quote"""
    quote = models.ForeignKey(Quote, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        unique_together = ['quote', 'product']
    
    def __str__(self):
        return f"{self.product.name} x {self.quantity}"
    
    @property
    def total_price(self):
        """Calculate total price for this item"""
        subtotal = self.quantity * self.unit_price
        discount = self.discount_amount
        if self.discount_percentage > 0:
            discount = (subtotal * self.discount_percentage) / 100
        return subtotal - discount
