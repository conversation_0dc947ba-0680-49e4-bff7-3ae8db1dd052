# Inventory & Billing Management System

A comprehensive inventory and billing management system built with Django and Flask, using MySQL as the database.

## Features

- **User Authentication & Role Management**: Secure login with Admin and Staff roles
- **Product Management**: Add, update, delete, and view products with stock tracking
- **Inventory Tracking**: Real-time stock updates and low-stock alerts
- **Billing & Invoicing**: Generate bills, calculate totals, and export PDF invoices
- **Sales Records & Reports**: Transaction history and business analytics
- **RESTful APIs**: CRUD operations for all major entities

## Tech Stack

- **Backend**: Django 4.2, Flask 2.3
- **Database**: MySQL
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **PDF Generation**: ReportLab, WeasyPrint
- **Authentication**: Django Auth, JWT

## Installation

1. Clone the repository
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (Linux/Mac)
4. Install dependencies: `pip install -r requirements.txt`
5. Copy `.env.example` to `.env` and configure your settings
6. Create MySQL database
7. Run Django migrations: `python manage.py migrate`
8. Create superuser: `python manage.py createsuperuser`
9. Run Django server: `python manage.py runserver`
10. Run Flask API server: `python flask_app/app.py`

## Project Structure

```
inventory_billing/
├── django_app/          # Django application
├── flask_app/           # Flask API application
├── static/              # Static files (CSS, JS, images)
├── media/               # User uploaded files
├── templates/           # HTML templates
├── requirements.txt     # Python dependencies
├── .env.example         # Environment variables template
└── README.md           # This file
```

## Usage

1. Access Django admin at `http://localhost:8000/admin/`
2. Access main application at `http://localhost:8000/`
3. Access Flask API at `http://localhost:5000/api/`

## API Endpoints

- `GET /api/products/` - List all products
- `POST /api/products/` - Create new product
- `GET /api/products/<id>/` - Get product details
- `PUT /api/products/<id>/` - Update product
- `DELETE /api/products/<id>/` - Delete product
- `GET /api/sales/` - List all sales
- `POST /api/sales/` - Create new sale

## License

This project is licensed under the MIT License.
