from django.urls import path
from . import views

urlpatterns = [
    # Dashboard
    path('', views.billing_dashboard, name='billing_dashboard'),
    
    # Sales/Invoices
    path('sales/', views.sale_list, name='sale_list'),
    path('sales/<int:pk>/', views.sale_detail, name='sale_detail'),
    path('sales/create/', views.create_sale, name='create_sale'),
    path('sales/<int:pk>/pdf/', views.generate_invoice_pdf_view, name='generate_invoice_pdf'),
    
    # POS
    path('pos/', views.pos_interface, name='pos_interface'),
    path('pos/process/', views.process_pos_sale, name='process_pos_sale'),
    
    # Customers
    path('customers/', views.customer_list, name='customer_list'),
    path('customers/create/', views.customer_create, name='customer_create'),
    
    # Payments
    path('sales/<int:sale_id>/add-payment/', views.add_payment, name='add_payment'),
]
