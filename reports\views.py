from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Avg, F, Q
from django.utils import timezone
from django.http import HttpResponse, JsonResponse
from datetime import datetime, timedelta
import json

from products.models import Product, Category
from billing.models import Sale, SaleItem, Customer
from inventory.models import StockMovement


@login_required
def reports_dashboard(request):
    """Reports dashboard with available reports"""
    return render(request, 'reports/dashboard.html')


@login_required
def sales_report(request):
    """Sales report with filtering options"""
    # Get filter parameters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    customer_id = request.GET.get('customer')
    payment_status = request.GET.get('payment_status')
    
    # Base queryset
    sales = Sale.objects.all()
    
    # Apply filters
    if date_from:
        sales = sales.filter(sale_date__date__gte=date_from)
    if date_to:
        sales = sales.filter(sale_date__date__lte=date_to)
    if customer_id:
        sales = sales.filter(customer_id=customer_id)
    if payment_status:
        sales = sales.filter(payment_status=payment_status)
    
    # Calculate totals
    totals = sales.aggregate(
        total_sales=Sum('total_amount'),
        total_paid=Sum('amount_paid'),
        count=Count('id'),
        avg_sale=Avg('total_amount')
    )
    
    # Group by payment status
    status_breakdown = sales.values('payment_status').annotate(
        count=Count('id'),
        total=Sum('total_amount')
    ).order_by('payment_status')
    
    # Top customers
    from django.db.models import Max
    top_customers = sales.values('customer__name', 'customer_name').annotate(
        total_spent=Sum('total_amount'),
        order_count=Count('id')
    ).order_by('-total_spent')[:10]
    
    context = {
        'sales': sales.order_by('-sale_date')[:50],  # Latest 50 sales
        'totals': totals,
        'status_breakdown': status_breakdown,
        'top_customers': top_customers,
        'customers': Customer.objects.filter(is_active=True),
        'payment_status_choices': Sale.PAYMENT_STATUS_CHOICES,
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'customer_id': customer_id,
            'payment_status': payment_status,
        }
    }
    
    return render(request, 'reports/sales_report.html', context)


@login_required
def product_performance_report(request):
    """Product performance report"""
    # Get filter parameters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    category_id = request.GET.get('category')
    
    # Base queryset for sale items
    sale_items = SaleItem.objects.select_related('product', 'sale')
    
    # Apply filters
    if date_from:
        sale_items = sale_items.filter(sale__sale_date__date__gte=date_from)
    if date_to:
        sale_items = sale_items.filter(sale__sale_date__date__lte=date_to)
    if category_id:
        sale_items = sale_items.filter(product__category_id=category_id)
    
    # Product performance data
    product_performance = sale_items.values(
        'product__name', 'product__sku', 'product__category__name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum(F('quantity') * F('unit_price')),
        total_profit=Sum(F('quantity') * (F('unit_price') - F('product__cost_price'))),
        order_count=Count('sale', distinct=True)
    ).order_by('-total_revenue')
    
    # Category performance
    category_performance = sale_items.values('product__category__name').annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum(F('quantity') * F('unit_price')),
        product_count=Count('product', distinct=True)
    ).order_by('-total_revenue')
    
    # Low performing products (products with no sales)
    sold_product_ids = sale_items.values_list('product_id', flat=True).distinct()
    low_performing = Product.objects.exclude(id__in=sold_product_ids).filter(is_active=True)
    
    context = {
        'product_performance': product_performance[:20],  # Top 20 products
        'category_performance': category_performance,
        'low_performing': low_performing[:10],  # 10 products with no sales
        'categories': Category.objects.filter(is_active=True),
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'category_id': category_id,
        }
    }
    
    return render(request, 'reports/product_performance.html', context)


@login_required
def inventory_report(request):
    """Inventory status report"""
    # Current inventory status
    products = Product.objects.filter(is_active=True).select_related('category')
    
    # Categorize products by stock status
    low_stock = products.filter(stock_quantity__lte=F('minimum_stock'))
    out_of_stock = products.filter(stock_quantity=0)
    overstock = products.filter(stock_quantity__gte=F('maximum_stock'))
    
    # Inventory valuation
    total_cost_value = products.aggregate(
        total=Sum(F('stock_quantity') * F('cost_price'))
    )['total'] or 0
    
    total_selling_value = products.aggregate(
        total=Sum(F('stock_quantity') * F('selling_price'))
    )['total'] or 0
    
    # Recent stock movements
    recent_movements = StockMovement.objects.select_related('product').order_by('-created_at')[:20]
    
    # Stock movement summary by type
    movement_summary = StockMovement.objects.values('movement_type').annotate(
        total_quantity=Sum('quantity'),
        count=Count('id')
    ).order_by('movement_type')
    
    context = {
        'total_products': products.count(),
        'low_stock_count': low_stock.count(),
        'out_of_stock_count': out_of_stock.count(),
        'overstock_count': overstock.count(),
        'total_cost_value': total_cost_value,
        'total_selling_value': total_selling_value,
        'potential_profit': total_selling_value - total_cost_value,
        'low_stock_products': low_stock[:10],
        'out_of_stock_products': out_of_stock[:10],
        'recent_movements': recent_movements,
        'movement_summary': movement_summary,
    }
    
    return render(request, 'reports/inventory_report.html', context)


@login_required
def financial_summary(request):
    """Financial summary report"""
    # Get date range (default to current month)
    today = timezone.now().date()
    month_start = today.replace(day=1)
    
    date_from = request.GET.get('date_from', month_start.strftime('%Y-%m-%d'))
    date_to = request.GET.get('date_to', today.strftime('%Y-%m-%d'))
    
    # Sales data
    sales = Sale.objects.filter(sale_date__date__range=[date_from, date_to])
    
    # Financial metrics
    financial_data = sales.aggregate(
        total_revenue=Sum('total_amount'),
        total_paid=Sum('amount_paid'),
        total_pending=Sum('total_amount') - Sum('amount_paid'),
        total_sales_count=Count('id'),
        avg_sale_value=Avg('total_amount')
    )
    
    # Calculate cost of goods sold
    sale_items = SaleItem.objects.filter(sale__sale_date__date__range=[date_from, date_to])
    cogs = sale_items.aggregate(
        total_cogs=Sum(F('quantity') * F('product__cost_price'))
    )['total_cogs'] or 0
    
    # Gross profit
    gross_profit = (financial_data['total_revenue'] or 0) - cogs
    gross_margin = (gross_profit / (financial_data['total_revenue'] or 1)) * 100 if financial_data['total_revenue'] else 0
    
    # Daily sales trend
    daily_sales = sales.extra(
        select={'day': 'DATE(sale_date)'}
    ).values('day').annotate(
        daily_total=Sum('total_amount'),
        daily_count=Count('id')
    ).order_by('day')
    
    # Payment method breakdown
    payment_methods = sales.values('payment_method').annotate(
        total=Sum('total_amount'),
        count=Count('id')
    ).order_by('-total')
    
    context = {
        'financial_data': financial_data,
        'cogs': cogs,
        'gross_profit': gross_profit,
        'gross_margin': gross_margin,
        'daily_sales': list(daily_sales),
        'payment_methods': payment_methods,
        'date_from': date_from,
        'date_to': date_to,
    }
    
    return render(request, 'reports/financial_summary.html', context)


@login_required
def customer_analysis(request):
    """Customer analysis report"""
    # Customer metrics
    customers = Customer.objects.filter(is_active=True)
    
    # Customer sales data
    customer_sales = customers.annotate(
        total_spent=Sum('sales__total_amount'),
        total_orders=Count('sales'),
        avg_order_value=Avg('sales__total_amount'),
        last_order_date=Max('sales__sale_date')
    ).filter(total_spent__isnull=False).order_by('-total_spent')
    
    # Customer segmentation
    high_value_customers = customer_sales.filter(total_spent__gte=1000)
    medium_value_customers = customer_sales.filter(total_spent__gte=500, total_spent__lt=1000)
    low_value_customers = customer_sales.filter(total_spent__lt=500)
    
    # New vs returning customers (last 30 days)
    thirty_days_ago = timezone.now().date() - timedelta(days=30)
    recent_sales = Sale.objects.filter(sale_date__date__gte=thirty_days_ago)
    
    new_customers = recent_sales.filter(customer__created_at__date__gte=thirty_days_ago).values('customer').distinct().count()
    returning_customers = recent_sales.exclude(customer__created_at__date__gte=thirty_days_ago).values('customer').distinct().count()
    
    context = {
        'total_customers': customers.count(),
        'customer_sales': customer_sales[:20],  # Top 20 customers
        'high_value_count': high_value_customers.count(),
        'medium_value_count': medium_value_customers.count(),
        'low_value_count': low_value_customers.count(),
        'new_customers': new_customers,
        'returning_customers': returning_customers,
    }
    
    return render(request, 'reports/customer_analysis.html', context)


@login_required
def export_sales_data(request):
    """Export sales data to CSV"""
    import csv
    from django.http import HttpResponse
    
    # Get filter parameters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    sales = Sale.objects.all()
    if date_from:
        sales = sales.filter(sale_date__date__gte=date_from)
    if date_to:
        sales = sales.filter(sale_date__date__lte=date_to)
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="sales_report.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['Invoice Number', 'Customer', 'Date', 'Total Amount', 'Payment Status', 'Payment Method'])
    
    for sale in sales:
        writer.writerow([
            sale.invoice_number,
            sale.customer_name,
            sale.sale_date.strftime('%Y-%m-%d'),
            sale.total_amount,
            sale.get_payment_status_display(),
            sale.get_payment_method_display()
        ])
    
    return response
