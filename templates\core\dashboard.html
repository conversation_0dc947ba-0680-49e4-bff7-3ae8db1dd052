{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - Inventory & Billing Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
            <small class="text-muted">Welcome back, {{ user.first_name|default:user.username }}!</small>
        </h1>
    </div>
</div>

<!-- Key Metrics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_products }}</h4>
                        <p class="mb-0">Total Products</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'product_list' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ low_stock_products }}</h4>
                        <p class="mb-0">Low Stock Items</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'low_stock_report' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ today_sales_total|floatformat:2 }}</h4>
                        <p class="mb-0">Today's Sales</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <small>{{ today_sales_count }} transactions</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ today_sales_count }}</h4>
                        <p class="mb-0">Today's Orders</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'sale_list' %}" class="text-white text-decoration-none">
                    View All <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and Alerts -->
<div class="row">
    <!-- Recent Sales -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-invoice"></i> Recent Sales</h5>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>
                                        <a href="{% url 'sale_detail' sale.pk %}">{{ sale.invoice_number }}</a>
                                    </td>
                                    <td>{{ sale.customer_name }}</td>
                                    <td>${{ sale.total_amount|floatformat:2 }}</td>
                                    <td>{{ sale.created_at|date:"M d, Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No recent sales found.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{% url 'sale_list' %}" class="btn btn-sm btn-primary">View All Sales</a>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alerts -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alerts</h5>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Current Stock</th>
                                    <th>Min Stock</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_items %}
                                <tr>
                                    <td>
                                        <a href="{% url 'product_detail' product.pk %}">{{ product.name }}</a>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                                    </td>
                                    <td>{{ product.minimum_stock }}</td>
                                    <td>
                                        <a href="{% url 'add_stock_movement' %}" class="btn btn-sm btn-outline-primary">Restock</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">All products are well stocked!</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{% url 'low_stock_report' %}" class="btn btn-sm btn-warning">View Full Report</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <a href="{% url 'pos_interface' %}" class="btn btn-success btn-lg w-100 mb-2">
                            <i class="fas fa-cash-register"></i><br>
                            <small>New Sale</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'product_create' %}" class="btn btn-primary btn-lg w-100 mb-2">
                            <i class="fas fa-plus"></i><br>
                            <small>Add Product</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'customer_create' %}" class="btn btn-info btn-lg w-100 mb-2">
                            <i class="fas fa-user-plus"></i><br>
                            <small>Add Customer</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'add_stock_movement' %}" class="btn btn-warning btn-lg w-100 mb-2">
                            <i class="fas fa-boxes"></i><br>
                            <small>Stock Movement</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'sales_report' %}" class="btn btn-secondary btn-lg w-100 mb-2">
                            <i class="fas fa-chart-line"></i><br>
                            <small>Sales Report</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'inventory_report' %}" class="btn btn-dark btn-lg w-100 mb-2">
                            <i class="fas fa-warehouse"></i><br>
                            <small>Inventory Report</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
