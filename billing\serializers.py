from rest_framework import serializers
from .models import Sale, SaleItem, Customer, Payment


class CustomerSerializer(serializers.ModelSerializer):
    total_purchases = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'id', 'name', 'email', 'phone', 'address', 'city', 'state',
            'postal_code', 'country', 'customer_type', 'company_name',
            'tax_id', 'is_active', 'created_at', 'total_purchases'
        ]
        read_only_fields = ['created_at']
    
    def get_total_purchases(self, obj):
        from django.db import models
        return obj.sales.aggregate(total=models.Sum('total_amount'))['total'] or 0


class SaleItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    total_price = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    
    class Meta:
        model = SaleItem
        fields = [
            'id', 'product', 'product_name', 'product_sku', 'quantity',
            'unit_price', 'discount_percentage', 'discount_amount', 'total_price'
        ]


class SaleSerializer(serializers.ModelSerializer):
    items = SaleItemSerializer(many=True, read_only=True)
    customer_name_display = serializers.CharField(source='customer.name', read_only=True)
    balance_due = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    is_fully_paid = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Sale
        fields = [
            'id', 'invoice_number', 'customer', 'customer_name', 'customer_name_display',
            'customer_phone', 'customer_email', 'sale_date', 'due_date',
            'subtotal', 'discount_amount', 'discount_percentage', 'tax_amount',
            'tax_percentage', 'total_amount', 'payment_status', 'payment_method',
            'amount_paid', 'balance_due', 'is_fully_paid', 'notes', 'terms_conditions',
            'created_at', 'items'
        ]
        read_only_fields = ['invoice_number', 'created_at', 'subtotal', 'total_amount']


class PaymentSerializer(serializers.ModelSerializer):
    sale_invoice = serializers.CharField(source='sale.invoice_number', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'sale', 'sale_invoice', 'amount', 'payment_method',
            'payment_date', 'reference_number', 'notes', 'created_at'
        ]
        read_only_fields = ['created_at']
