# Database Configuration
DB_NAME=inventory_billing_db
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# Django Settings
DJANGO_SECRET_KEY=your-secret-key-here
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

# Flask Settings
FLASK_SECRET_KEY=your-flask-secret-key
FLASK_DEBUG=True

# JWT Settings
JWT_SECRET_KEY=your-jwt-secret-key

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True

# Other Settings
MEDIA_ROOT=media/
STATIC_ROOT=static/
