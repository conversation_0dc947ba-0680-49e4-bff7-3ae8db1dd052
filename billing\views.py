from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Sum
from django.core.paginator import Paginator
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.template.loader import render_to_string
from datetime import datetime, timedelta

from .models import Sale, SaleItem, Customer, Payment, Quote
from products.models import Product
from .forms import SaleForm, CustomerForm, PaymentForm
from .utils import generate_invoice_pdf


@login_required
def billing_dashboard(request):
    """Billing dashboard with key metrics"""
    # Get today's sales
    today = timezone.now().date()
    today_sales = Sale.objects.filter(created_at__date=today).aggregate(
        total=Sum('total_amount'),
        count=models.Count('id')
    )
    
    # Get this month's sales
    from django.db import models
    month_start = today.replace(day=1)
    month_sales = Sale.objects.filter(created_at__date__gte=month_start).aggregate(
        total=Sum('total_amount'),
        count=models.Count('id')
    )
    
    # Pending payments
    pending_amount = Sale.objects.filter(payment_status__in=['pending', 'partial']).aggregate(
        total=Sum('total_amount') - Sum('amount_paid')
    )['total'] or 0
    
    # Recent sales
    recent_sales = Sale.objects.select_related('customer').order_by('-created_at')[:5]
    
    # Overdue invoices
    overdue_invoices = Sale.objects.filter(
        due_date__lt=today,
        payment_status__in=['pending', 'partial']
    ).count()
    
    context = {
        'today_sales_total': today_sales['total'] or 0,
        'today_sales_count': today_sales['count'] or 0,
        'month_sales_total': month_sales['total'] or 0,
        'month_sales_count': month_sales['count'] or 0,
        'pending_amount': pending_amount,
        'recent_sales': recent_sales,
        'overdue_invoices': overdue_invoices,
    }
    
    return render(request, 'billing/dashboard.html', context)


@login_required
def sale_list(request):
    """List all sales/invoices"""
    sales = Sale.objects.select_related('customer').all()
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        sales = sales.filter(
            Q(invoice_number__icontains=search_query) |
            Q(customer_name__icontains=search_query) |
            Q(customer__name__icontains=search_query)
        )
    
    # Payment status filter
    payment_status = request.GET.get('payment_status')
    if payment_status:
        sales = sales.filter(payment_status=payment_status)
    
    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        sales = sales.filter(created_at__date__gte=date_from)
    if date_to:
        sales = sales.filter(created_at__date__lte=date_to)
    
    # Pagination
    paginator = Paginator(sales, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'payment_status_choices': Sale.PAYMENT_STATUS_CHOICES,
        'search_query': search_query,
        'selected_payment_status': payment_status,
        'date_from': date_from,
        'date_to': date_to,
    }
    
    return render(request, 'billing/sale_list.html', context)


@login_required
def sale_detail(request, pk):
    """Sale detail view"""
    sale = get_object_or_404(Sale, pk=pk)
    items = sale.items.select_related('product').all()
    payments = sale.payments.all()
    
    context = {
        'sale': sale,
        'items': items,
        'payments': payments,
    }
    
    return render(request, 'billing/sale_detail.html', context)


@login_required
def create_sale(request):
    """Create new sale/invoice"""
    if request.method == 'POST':
        form = SaleForm(request.POST)
        if form.is_valid():
            sale = form.save(commit=False)
            sale.created_by = request.user
            
            # Generate invoice number
            last_sale = Sale.objects.order_by('-id').first()
            if last_sale:
                last_number = int(last_sale.invoice_number.split('-')[-1])
                sale.invoice_number = f"INV-{last_number + 1:06d}"
            else:
                sale.invoice_number = "INV-000001"
            
            sale.save()
            messages.success(request, f'Invoice {sale.invoice_number} created successfully.')
            return redirect('sale_detail', pk=sale.pk)
    else:
        form = SaleForm()
    
    return render(request, 'billing/sale_form.html', {
        'form': form,
        'title': 'Create New Invoice'
    })


@login_required
def pos_interface(request):
    """Point of Sale interface"""
    products = Product.objects.filter(is_active=True, stock_quantity__gt=0).select_related('category')
    categories = products.values_list('category__name', flat=True).distinct()
    
    context = {
        'products': products,
        'categories': categories,
    }
    
    return render(request, 'billing/pos_interface.html', context)


@login_required
def process_pos_sale(request):
    """Process POS sale"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            
            # Create sale
            sale = Sale.objects.create(
                customer_name=data.get('customer_name', 'Walk-in Customer'),
                customer_phone=data.get('customer_phone', ''),
                payment_method=data.get('payment_method', 'cash'),
                payment_status='paid',
                created_by=request.user
            )
            
            # Generate invoice number
            last_sale = Sale.objects.exclude(pk=sale.pk).order_by('-id').first()
            if last_sale:
                last_number = int(last_sale.invoice_number.split('-')[-1])
                sale.invoice_number = f"INV-{last_number + 1:06d}"
            else:
                sale.invoice_number = "INV-000001"
            
            # Add items
            total_amount = 0
            for item_data in data['items']:
                product = Product.objects.get(id=item_data['product_id'])
                quantity = int(item_data['quantity'])
                unit_price = float(item_data['unit_price'])
                
                if not product.can_sell(quantity):
                    return JsonResponse({
                        'success': False,
                        'error': f'Insufficient stock for {product.name}'
                    })
                
                sale_item = SaleItem.objects.create(
                    sale=sale,
                    product=product,
                    quantity=quantity,
                    unit_price=unit_price
                )
                total_amount += sale_item.total_price
            
            # Update sale totals
            sale.subtotal = total_amount
            sale.total_amount = total_amount
            sale.amount_paid = total_amount
            sale.save()
            
            return JsonResponse({
                'success': True,
                'sale_id': sale.id,
                'invoice_number': sale.invoice_number
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def customer_list(request):
    """List all customers"""
    customers = Customer.objects.all()
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        customers = customers.filter(
            Q(name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(customers, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }
    
    return render(request, 'billing/customer_list.html', context)


@login_required
def customer_create(request):
    """Create new customer"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save(commit=False)
            customer.created_by = request.user
            customer.save()
            messages.success(request, f'Customer "{customer.name}" created successfully.')
            return redirect('customer_list')
    else:
        form = CustomerForm()
    
    return render(request, 'billing/customer_form.html', {
        'form': form,
        'title': 'Add New Customer'
    })


@login_required
def generate_invoice_pdf_view(request, pk):
    """Generate PDF invoice"""
    sale = get_object_or_404(Sale, pk=pk)
    
    try:
        pdf_content = generate_invoice_pdf(sale)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{sale.invoice_number}.pdf"'
        return response
    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('sale_detail', pk=pk)


@login_required
def add_payment(request, sale_id):
    """Add payment to a sale"""
    sale = get_object_or_404(Sale, pk=sale_id)
    
    if request.method == 'POST':
        form = PaymentForm(request.POST)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.sale = sale
            payment.created_by = request.user
            payment.save()
            messages.success(request, 'Payment recorded successfully.')
            return redirect('sale_detail', pk=sale.pk)
    else:
        form = PaymentForm()
    
    context = {
        'form': form,
        'sale': sale,
        'title': f'Add Payment for {sale.invoice_number}'
    }
    
    return render(request, 'billing/payment_form.html', context)
