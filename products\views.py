from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods

from .models import Product, Category, Supplier
from .forms import ProductForm, CategoryForm, SupplierForm


@login_required
def product_list(request):
    """List all products with search and filtering"""
    products = Product.objects.select_related('category', 'supplier').all()
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(sku__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Category filter
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)
    
    # Stock filter
    stock_filter = request.GET.get('stock')
    if stock_filter == 'low':
        from django.db import models
        products = products.filter(stock_quantity__lte=models.F('minimum_stock'))
    elif stock_filter == 'out':
        products = products.filter(stock_quantity=0)
    
    # Pagination
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get categories for filter dropdown
    categories = Category.objects.filter(is_active=True)
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id,
        'stock_filter': stock_filter,
    }
    
    return render(request, 'products/product_list.html', context)


@login_required
def product_detail(request, pk):
    """Product detail view"""
    product = get_object_or_404(Product, pk=pk)
    return render(request, 'products/product_detail.html', {'product': product})


@login_required
def product_create(request):
    """Create new product"""
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save(commit=False)
            product.created_by = request.user
            product.save()
            messages.success(request, f'Product "{product.name}" created successfully.')
            return redirect('product_detail', pk=product.pk)
    else:
        form = ProductForm()
    
    return render(request, 'products/product_form.html', {
        'form': form,
        'title': 'Add New Product'
    })


@login_required
def product_update(request, pk):
    """Update existing product"""
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            product = form.save(commit=False)
            product.updated_by = request.user
            product.save()
            messages.success(request, f'Product "{product.name}" updated successfully.')
            return redirect('product_detail', pk=product.pk)
    else:
        form = ProductForm(instance=product)
    
    return render(request, 'products/product_form.html', {
        'form': form,
        'product': product,
        'title': f'Edit {product.name}'
    })


@login_required
@require_http_methods(["POST"])
def product_delete(request, pk):
    """Delete product"""
    product = get_object_or_404(Product, pk=pk)
    product_name = product.name
    product.delete()
    messages.success(request, f'Product "{product_name}" deleted successfully.')
    return redirect('product_list')


@login_required
def category_list(request):
    """List all categories"""
    categories = Category.objects.all().order_by('name')
    return render(request, 'products/category_list.html', {'categories': categories})


@login_required
def category_create(request):
    """Create new category"""
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            category = form.save(commit=False)
            category.created_by = request.user
            category.save()
            messages.success(request, f'Category "{category.name}" created successfully.')
            return redirect('category_list')
    else:
        form = CategoryForm()
    
    return render(request, 'products/category_form.html', {
        'form': form,
        'title': 'Add New Category'
    })


@login_required
def supplier_list(request):
    """List all suppliers"""
    suppliers = Supplier.objects.all().order_by('name')
    return render(request, 'products/supplier_list.html', {'suppliers': suppliers})


@login_required
def get_product_info(request, pk):
    """AJAX endpoint to get product information"""
    try:
        product = Product.objects.get(pk=pk)
        data = {
            'id': product.id,
            'name': product.name,
            'sku': product.sku,
            'selling_price': float(product.selling_price),
            'stock_quantity': product.stock_quantity,
            'unit': product.unit,
        }
        return JsonResponse(data)
    except Product.DoesNotExist:
        return JsonResponse({'error': 'Product not found'}, status=404)
