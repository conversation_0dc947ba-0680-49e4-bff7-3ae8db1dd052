from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Sum
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.utils import timezone

from .models import StockMovement, PurchaseOrder, StockAlert, InventoryAdjustment
from products.models import Product
from .forms import StockMovementForm, PurchaseOrderForm, InventoryAdjustmentForm


@login_required
def inventory_dashboard(request):
    """Inventory dashboard with key metrics"""
    # Get inventory statistics
    from django.db import models
    total_products = Product.objects.count()
    low_stock_count = Product.objects.filter(stock_quantity__lte=models.F('minimum_stock')).count()
    out_of_stock_count = Product.objects.filter(stock_quantity=0).count()
    
    # Recent stock movements
    recent_movements = StockMovement.objects.select_related('product').order_by('-created_at')[:10]
    
    # Active alerts
    active_alerts = StockAlert.objects.filter(is_resolved=False).select_related('product')[:5]
    
    # Pending purchase orders
    pending_pos = PurchaseOrder.objects.filter(status__in=['draft', 'sent']).count()
    
    context = {
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'recent_movements': recent_movements,
        'active_alerts': active_alerts,
        'pending_pos': pending_pos,
    }
    
    return render(request, 'inventory/dashboard.html', context)


@login_required
def stock_movements(request):
    """List all stock movements"""
    movements = StockMovement.objects.select_related('product').all()
    
    # Filter by product
    product_id = request.GET.get('product')
    if product_id:
        movements = movements.filter(product_id=product_id)
    
    # Filter by movement type
    movement_type = request.GET.get('type')
    if movement_type:
        movements = movements.filter(movement_type=movement_type)
    
    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        movements = movements.filter(created_at__date__gte=date_from)
    if date_to:
        movements = movements.filter(created_at__date__lte=date_to)
    
    # Pagination
    paginator = Paginator(movements, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'movement_types': StockMovement.MOVEMENT_TYPES,
        'selected_product': product_id,
        'selected_type': movement_type,
        'date_from': date_from,
        'date_to': date_to,
    }
    
    return render(request, 'inventory/stock_movements.html', context)


@login_required
def add_stock_movement(request):
    """Add new stock movement"""
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            movement = form.save(commit=False)
            movement.created_by = request.user
            movement.save()
            messages.success(request, 'Stock movement recorded successfully.')
            return redirect('stock_movements')
    else:
        form = StockMovementForm()
    
    return render(request, 'inventory/stock_movement_form.html', {
        'form': form,
        'title': 'Add Stock Movement'
    })


@login_required
def purchase_orders(request):
    """List all purchase orders"""
    pos = PurchaseOrder.objects.select_related('supplier').all()
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        pos = pos.filter(status=status)
    
    # Search by PO number or supplier
    search = request.GET.get('search')
    if search:
        pos = pos.filter(
            Q(po_number__icontains=search) |
            Q(supplier__name__icontains=search)
        )
    
    # Pagination
    paginator = Paginator(pos, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_choices': PurchaseOrder.STATUS_CHOICES,
        'selected_status': status,
        'search_query': search,
    }
    
    return render(request, 'inventory/purchase_orders.html', context)


@login_required
def create_purchase_order(request):
    """Create new purchase order"""
    if request.method == 'POST':
        form = PurchaseOrderForm(request.POST)
        if form.is_valid():
            po = form.save(commit=False)
            po.created_by = request.user
            po.save()
            messages.success(request, f'Purchase Order {po.po_number} created successfully.')
            return redirect('purchase_order_detail', pk=po.pk)
    else:
        form = PurchaseOrderForm()
    
    return render(request, 'inventory/purchase_order_form.html', {
        'form': form,
        'title': 'Create Purchase Order'
    })


@login_required
def purchase_order_detail(request, pk):
    """Purchase order detail view"""
    po = get_object_or_404(PurchaseOrder, pk=pk)
    items = po.items.select_related('product').all()
    
    context = {
        'po': po,
        'items': items,
    }
    
    return render(request, 'inventory/purchase_order_detail.html', context)


@login_required
def stock_alerts(request):
    """List all stock alerts"""
    alerts = StockAlert.objects.select_related('product').all()
    
    # Filter by resolved status
    resolved = request.GET.get('resolved')
    if resolved == 'true':
        alerts = alerts.filter(is_resolved=True)
    elif resolved == 'false':
        alerts = alerts.filter(is_resolved=False)
    
    # Filter by alert type
    alert_type = request.GET.get('type')
    if alert_type:
        alerts = alerts.filter(alert_type=alert_type)
    
    # Pagination
    paginator = Paginator(alerts, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'alert_types': StockAlert.ALERT_TYPES,
        'selected_type': alert_type,
        'resolved_filter': resolved,
    }
    
    return render(request, 'inventory/stock_alerts.html', context)


@login_required
def resolve_alert(request, pk):
    """Resolve a stock alert"""
    alert = get_object_or_404(StockAlert, pk=pk)
    alert.resolve()
    messages.success(request, 'Alert resolved successfully.')
    return redirect('stock_alerts')


@login_required
def low_stock_report(request):
    """Generate low stock report"""
    low_stock_products = Product.objects.filter(
        stock_quantity__lte=models.F('minimum_stock')
    ).select_related('category').order_by('stock_quantity')
    
    context = {
        'products': low_stock_products,
        'total_count': low_stock_products.count(),
    }
    
    return render(request, 'inventory/low_stock_report.html', context)


@login_required
def inventory_valuation(request):
    """Calculate inventory valuation"""
    products = Product.objects.filter(is_active=True, stock_quantity__gt=0)
    
    total_cost_value = 0
    total_selling_value = 0
    valuation_data = []
    
    for product in products:
        cost_value = product.stock_quantity * product.cost_price
        selling_value = product.stock_quantity * product.selling_price
        
        total_cost_value += cost_value
        total_selling_value += selling_value
        
        valuation_data.append({
            'product': product,
            'cost_value': cost_value,
            'selling_value': selling_value,
            'potential_profit': selling_value - cost_value,
        })
    
    context = {
        'valuation_data': valuation_data,
        'total_cost_value': total_cost_value,
        'total_selling_value': total_selling_value,
        'total_potential_profit': total_selling_value - total_cost_value,
    }
    
    return render(request, 'inventory/valuation_report.html', context)
